// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "rknn_yolox.h"
#include "rga.h"
//#include "im2d.h"
namespace czcv_camera
{	
    Status YoloxRKNN::init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType)
    {	
        if(modelConfig.size() <5)
        {
            LOGE("modelConfig size not valid!");
            return CZCV_PARAM_ERR;
        }
		_modelType = modelType;

		int ret = rknn_init(&ctx, (void*)modelConfig[4].c_str(), 0, 0, NULL);
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		rknn_core_mask core_mask = RKNN_NPU_CORE_0;
		if (false == is_primary())
		{
			core_mask = RKNN_NPU_CORE_1;
		}

 		// ret = rknn_set_core_mask(ctx, core_mask);
		// if (ret < 0)
		// {
		// 	LOGE("rknn_set_core_mask error ret=%d\n", ret);
		// 	return -1;
		// }

		ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		input_attrs.resize(io_num.n_input);
		memset(input_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_input);
		for (int i = 0; i < io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				LOGE("rknn_init error ret=%d\n", ret);
				return -1;
			}
		}

		output_attrs.resize(io_num.n_output);
		memset(output_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_output);
		for (int i = 0; i < io_num.n_output; i++)
		{
			output_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
		}

		if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			//LOGE("model is NCHW input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputChannel = input_attrs[0].dims[1];
			_inputHeight = input_attrs[0].dims[2];
			_inputWidth = input_attrs[0].dims[3];
		}
		else
		{
			//LOGE("model is NHWC input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputHeight = input_attrs[0].dims[1];
			_inputWidth = input_attrs[0].dims[2];
			_inputChannel = input_attrs[0].dims[3];
		}
		

		_rgaInterfacePtr = rgaInterfacePtr;
		
		this->rgamat_det_pad.handle = _rgaInterfacePtr->malloc_rga(&this->rgamat_det_pad.viraddr, &this->rgamat_det_pad.phyaddr,this->_inputWidth, this->_inputHeight, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);		
		
		input_attrs[0].type = RKNN_TENSOR_UINT8;
		// default fmt is NHWC, npu only support NHWC in zero copy mode
		input_attrs[0].fmt = RKNN_TENSOR_NHWC;
		input_mems[0]      = rknn_create_mem(ctx, input_attrs[0].size_with_stride);

		#define ALIGN 8
		int wstride = _inputWidth + (ALIGN - _inputWidth % ALIGN) % ALIGN;
  		int hstride = _inputHeight;
		//rga_buffer_t dst;
		//dst = wrapbuffer_fd_t(input_mems[0]->fd, _inputWidth, _inputHeight, wstride, hstride, RK_FORMAT_RGB_888);
		_rgaInterfacePtr->wrapbuffer_fd(input_mems[0]->fd, _inputWidth, _inputHeight, wstride, hstride, RK_FORMAT_RGB_888);
        
		// im_rect      dst_rect;
		// memset(&dst_rect, 0, sizeof(dst_rect));
		// ret = imcheck({}, dst, {}, dst_rect, IM_COLOR_FILL);
		// if (IM_STATUS_NOERROR != ret) {
		// 	LOGE("%d, check error! %s", __LINE__, imStrError((IM_STATUS)ret));
		// 	return -1;
		// }

		ret = rknn_set_io_mem(ctx, input_mems[0], &input_attrs[0]);
		if (ret < 0) {
			LOGE("rknn_set_io_mem fail! ret=%d\n", ret);
			return -1;
		}

		int output_size = output_attrs[0].n_elems * sizeof(float);
		// default output type is depend on model, this require float32 to compute top5
		output_attrs[0].type = RKNN_TENSOR_FLOAT32;
		output_mems[0]  = rknn_create_mem(ctx, output_size);

		int num_grid = output_attrs[0].dims[1];

		int outwstride = num_grid + (ALIGN - num_grid % ALIGN) % ALIGN;
		int outhstride = output_attrs[0].dims[2];
		_rgaInterfacePtr->wrapbuffer_fd(output_mems[0]->fd, num_grid, output_attrs[0].dims[2], outwstride, outhstride, RK_FORMAT_RGBA_8888); 			
		ret = rknn_set_io_mem(ctx, output_mems[0], &output_attrs[0]);		
		if (ret < 0) {
			LOGE("rknn_set_io_mem fail! ret=%d\n", ret);
			return -1;
		}   
		
		return CZCV_OK;
    }


	std::string YoloxRKNN::fdLoadFile(std::string path) 
	{
		std::ifstream file(path, std::ios::binary);
		if (file.is_open()) {
			file.seekg(0, file.end);
			int size = file.tellg();
			char* content = new char[size];
			file.seekg(0, file.beg);
			file.read(content, size);
			std::string fileContent;
			fileContent.assign(content, size);
			delete[] content;
			file.close();
			return fileContent;
		}
		else {
			return "";
		}
	}
	
	void YoloxRKNN::generate_grids_and_stride(const int target_size,
										std::vector<int>& strides, std::vector<GridAndStride>& grid_strides)
	{
		int sum_grid = 0;
		for (int i = 0; i < (int)strides.size(); i++)
		{
			int stride = strides[i];
			int num_grid = target_size / stride;
			for (int g1 = 0; g1 < num_grid; g1++)
			{
				for (int g0 = 0; g0 < num_grid; g0++)
				{
					sum_grid += 1;
					GridAndStride gs;
					gs.grid0 = g0;
					gs.grid1 = g1;
					gs.stride = stride;
					grid_strides.push_back(gs);
				}
			}
		}
	}

	void YoloxRKNN::qsort_descent_inplace(std::vector<Object>& objects, int left, int right)
	{
		int i = left;
		int j = right;
		float p = objects[(left + right) / 2].prob;

		while (i <= j)
		{
			while (objects[i].prob > p)
				i++;

			while (objects[j].prob < p)
				j--;

			if (i <= j)
			{
				// swap
				std::swap(objects[i], objects[j]);

				i++;
				j--;
			}
		}

// #pragma omp parallel sections
		{
// #pragma omp section
			{
				if (left < j) qsort_descent_inplace(objects, left, j);
			}
// #pragma omp section
			{
				if (i < right) qsort_descent_inplace(objects, i, right);
			}
		}
	}

	void YoloxRKNN::qsort_descent_inplace(std::vector<Object>& objects)
	{
		if (objects.empty())
			return;

		qsort_descent_inplace(objects, 0, objects.size() - 1);
	}
	
	
	void YoloxRKNN::generate_yolox_proposals(std::vector<GridAndStride> grid_strides,
									float * pred_mat, int num_grid, int num_class, float conf_thre,std::vector<Object>& proposals)
	{
		for (int grid_index = 0; grid_index < num_grid; grid_index++)
		{
			const float *offset_obj_cls_ptr =
				pred_mat + (grid_index * (num_class + 5));
			const int grid0 = grid_strides[grid_index].grid0;
			const int grid1 = grid_strides[grid_index].grid1;
			const int stride = grid_strides[grid_index].stride;
			
			float x_center = (offset_obj_cls_ptr[0] + grid0) * stride;
			float y_center = (offset_obj_cls_ptr[1] + grid1) * stride;
			float w = exp(offset_obj_cls_ptr[2]) * stride;
			float h = exp(offset_obj_cls_ptr[3]) * stride;
			float x0 = x_center - w * 0.5f;
			float y0 = y_center - h * 0.5f;
			
			float box_objectness = offset_obj_cls_ptr[4];

			for (int class_idx = 0; class_idx < num_class; class_idx++)
			{
				float box_cls_score = offset_obj_cls_ptr[5 + class_idx];
				float box_prob = box_objectness * box_cls_score;
				//head thresh is 0.1
				if ((box_prob > conf_thre) || ((1 == class_idx) && (box_prob > 0.1f)))
				{
					Object obj;
					obj.rect.x = x0;
					obj.rect.y = y0;
					obj.rect.width = w;
					obj.rect.height = h;
					obj.label = class_idx;
					obj.prob = box_prob;
					proposals.push_back(obj);
				}
			} // class loop
		}
	}

	
	inline float YoloxRKNN::intersection_area(const Object& a, const Object& b)
	{
		cv::Rect_<float> inter = a.rect & b.rect;
		return inter.area();
	}

	void YoloxRKNN::nms_sorted_bboxes(const std::vector<Object>& objects, 
								std::vector<int>& picked, float nms_threshold)
	{
		picked.clear();

		const int n = objects.size();

		std::vector<float> areas(n);
		for (int i = 0; i < n; i++)
		{
			areas[i] = objects[i].rect.area();
		}

		for (int i = 0; i < n; i++)
		{
			const Object& a = objects[i];

			int keep = 1;
			for (int j = 0; j < (int)picked.size(); j++)
			{
				const Object& b = objects[picked[j]];

				// intersection over union
				float inter_area = intersection_area(a, b);
				float union_area = areas[i] + areas[picked[j]] - inter_area;
				// float IoU = inter_area / union_area
				if (inter_area / union_area > nms_threshold)
					keep = 0;
			}

			if (keep)
				picked.push_back(i);
		}
	}
	
    Status YoloxRKNN::run(DetInputOutput &inputOutput)
    {
        cv::Mat bgr;
        inputOutput.ref_frame_to(bgr);
		int frame_h = inputOutput.get_frame_h();
		int frame_w = inputOutput.get_frame_w();
		int frame_channels = inputOutput.get_frame_channels();

		LOGE("YoloxRKNN::run: %d %d\n", frame_h, frame_w);

		int img_height = frame_h;
		if (frame_channels == 1)
		{
			img_height = img_height * 2 / 3;
		}
		int img_width = frame_w;
		int w = img_width;
		int h = img_height;
		float scale = 1.f;
		if (w > h)
		{
			scale = (float)_inputWidth / w;
			w = _inputWidth;
			h = h * scale;			
		}
		else
		{
			scale = (float)_inputHeight / h;
			h = _inputHeight;
			w = w * scale;
		}
	
		cv::Mat mat_rs;
		int wpad = _inputWidth - w;
		int hpad = _inputHeight - h;
		int ret;
		//cv::Mat bgr_resize;
		if (frame_channels == 4)
		{
			cv::resize(bgr, mat_rs, cv::Size(w, h), 0, 0, cv::INTER_LINEAR);
			cv::cvtColor(mat_rs, mat_rs, cv::COLOR_RGBA2BGR);
		}
		else if (frame_channels == 1)
		{
			w -= (w & 1);
			h -= (h & 1);
			wpad = _inputWidth - w;
			hpad = _inputHeight - h;
			
			cv::Mat dstnv21 = cv::Mat::zeros(h * 3 / 2, w, CV_8UC1);
			cv::Rect rect;
			cv::Rect dstRect;
			nv21resize(bgr, dstnv21, rect, dstRect);
			mat_rs = dstnv21;
			int code =  cv::COLOR_YUV2BGR_NV21;
			if (NV12_format == inputOutput.format())
			{
				code = cv::COLOR_YUV2BGR_NV12;
			}
			cv::cvtColor(mat_rs, mat_rs, code);
		}
		else
		{
			//int ret = _rgaInterfacePtr->mat_fill(rgamat_det_pad.phyaddr,_inputWidth,_inputHeight,114,114,114);
			//ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.get_input_phyadrr(),rgamat_det_pad.phyaddr,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows,_inputWidth,_inputHeight,0,0,w,h);

			//LOGE("_rgaInterfacePtr->mat_fill\n");
			ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,114,114,114);
			//LOGE("_rgaInterfacePtr->cropScaleRgb\n");
			ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.get_input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows,_inputWidth,_inputHeight,0,0,w,h);
		}

		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = _inputWidth * _inputHeight * _inputChannel;
		inputs[0].fmt = RKNN_TENSOR_NHWC;
		inputs[0].pass_through = 0;
		inputs[0].buf = rgamat_det_pad.viraddr;
		//memcpy(input_mems[0]->virt_addr, rgamat_det_pad.viraddr, input_attrs[0].size);

		//rknn_inputs_set(ctx, io_num.n_input, inputs);

		rknn_output outputs[io_num.n_output];
		memset(outputs, 0, sizeof(outputs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			outputs[i].index = i;
			outputs[i].want_float = 1;
		}
		
		// 执行推理
		ret = rknn_run(ctx, NULL);		
		
		//ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);

		static const int stride_arr[] = { 8, 16, 32 }; 
		std::vector<int> strides(stride_arr, stride_arr + sizeof(stride_arr) / sizeof(stride_arr[0]));

		std::vector<GridAndStride> grid_strides;
		int num_grid = output_attrs[0].dims[1];
		int num_class = output_attrs[0].dims[2] - 5;
		std::vector<Object> proposals;
		//float *pred_mat = (float *)outputs[0].buf;
		float* pred_mat = (float *)output_mems[0]->virt_addr;
	
		generate_grids_and_stride(_inputWidth, strides, grid_strides);
		generate_yolox_proposals(grid_strides, pred_mat,num_grid, num_class, this->conf_thres(), proposals);		
   
		qsort_descent_inplace(proposals);
  
		std::vector<int> picked;
		nms_sorted_bboxes(proposals, picked, this->nms_thres());
		
		int count = picked.size();
	
		for (int i = 0; i < count; i++)
		{
			Object& obj = proposals[picked[i]];

			// adjust offset to original unpadded
			float x0 = (obj.rect.x) / scale;
			float y0 = (obj.rect.y) / scale;
			float x1 = (obj.rect.x + obj.rect.width) / scale;
			float y1 = (obj.rect.y + obj.rect.height) / scale;

			// clip
			x0 = (std::max)((std::min)(x0, (float)(img_width - 1)), 0.f);
			y0 = (std::max)((std::min)(y0, (float)(img_height - 1)), 0.f);
			x1 = (std::max)((std::min)(x1, (float)(img_width - 1)), 0.f);
			y1 = (std::max)((std::min)(y1, (float)(img_height - 1)), 0.f);

			BboxF bboxF(x0, y0, x1, y1, obj.prob, obj.label);
        	inputOutput.push_one_bbox(bboxF); 
		}

		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);

        return CZCV_OK;
    }

}//namespace czcv_mobile

