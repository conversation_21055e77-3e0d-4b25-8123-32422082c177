@echo off
echo ========================================
echo Building czcv_camera for Android ARM64
echo ========================================

REM Set Android NDK path
set ANDROID_NDK=D:\Package\android-ndk-r23c
echo Using Android NDK: %ANDROID_NDK%

REM Check if NDK exists
if not exist "%ANDROID_NDK%\build\cmake\android.toolchain.cmake" (
    echo Error: Android NDK toolchain not found at %ANDROID_NDK%
    echo Please install Android NDK and update the path in this script
    pause
    exit /b 1
)

echo NDK found successfully!
echo.

REM Build third-party libraries first (optional - uncomment if needed)
REM echo Building third-party libraries...
REM cd third_party
REM call build_all_android_arm64.bat
REM if %errorlevel% neq 0 (
REM     echo Error: Third-party libraries build failed
REM     pause
REM     exit /b 1
REM )
REM cd ..
REM echo Third-party libraries built successfully!
REM echo.

REM Create and enter build directory
echo Creating build directory...
if not exist build_android_arm64-v8a mkdir build_android_arm64-v8a
cd build_android_arm64-v8a

echo Configuring project with CMake...
cmake -G "MSYS Makefiles" ^
    -DDst_Platform=android_v8a ^
    -DBUILD_BENCHMARK=OFF ^
    -DBUILD_TESTS=OFF ^
    -DCMAKE_TOOLCHAIN_FILE="%ANDROID_NDK%\build\cmake\android.toolchain.cmake" ^
    -DCMAKE_ANDROID_NDK="%ANDROID_NDK%" ^
    -DANDROID_NATIVE_API_LEVEL=24 ^
    -DANDROID_STL=c++_shared ^
    -DANDROID_ABI=arm64-v8a ^
    -DCMAKE_BUILD_TYPE=Release ^
    ..

if %errorlevel% neq 0 (
    echo Error: CMake configuration failed
    cd ..
    pause
    exit /b 1
)

echo CMake configuration completed successfully!
echo.

echo Building project...
make -j%NUMBER_OF_PROCESSORS%

if %errorlevel% neq 0 (
    echo Error: Build failed
    cd ..
    pause
    exit /b 1
)

echo Build completed successfully!
echo.

REM Go back to project root
cd ..

REM Check if output directory and library exist
if exist "output\android_v8a\libs\libczcv_camera.so" (
    echo Library built successfully: output\android_v8a\libs\libczcv_camera.so
    
    REM Strip the library (optional)
    echo Stripping library...
    if exist "%ANDROID_NDK%\toolchains\llvm\prebuilt\windows-x86_64\bin\llvm-strip.exe" (
        "%ANDROID_NDK%\toolchains\llvm\prebuilt\windows-x86_64\bin\llvm-strip.exe" "output\android_v8a\libs\libczcv_camera.so"
        echo Library stripped successfully!
    ) else (
        echo Warning: llvm-strip not found, skipping strip step
    )
    
    echo.
    echo ========================================
    echo Build Summary:
    echo ========================================
    echo Target Platform: Android ARM64 (arm64-v8a)
    echo Output Library: output\android_v8a\libs\libczcv_camera.so
    echo Build Type: Release
    echo ========================================
    
) else (
    echo Error: Library not found in expected location
    echo Expected: output\android_v8a\libs\libczcv_camera.so
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
pause
