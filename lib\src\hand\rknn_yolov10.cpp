// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.
#include <numeric>
#include "rknn_yolov10.h"
#include "rga.h"
//#include "im2d.h"
namespace czcv_camera
{	
    Status Yolov10RKNN::init(std::vector<std::string> modelConfig, std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType)
    {	
        if(modelConfig.size() <1)
        {
            LOGE("modelConfig size not valid!");
            return CZCV_PARAM_ERR;
        }
		_modelType = modelType;
	
		int ret = rknn_init(&ctx, (void*)modelConfig[0].c_str(), 0, 0, NULL);
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		rknn_core_mask core_mask = RKNN_NPU_CORE_0;
		if (false == is_primary())
		{
			core_mask = RKNN_NPU_CORE_1;
		}

 		// ret = rknn_set_core_mask(ctx, core_mask);
		// if (ret < 0)
		// {
		// 	LOGE("rknn_set_core_mask error ret=%d\n", ret);
		// 	return -1;
		// }

		ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		input_attrs.resize(io_num.n_input);
		memset(input_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_input);
		for (int i = 0; i < io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				LOGE("rknn_init error ret=%d\n", ret);
				return -1;
			}
		}

		output_attrs.resize(io_num.n_output);
		memset(output_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_output);
		for (int i = 0; i < io_num.n_output; i++)
		{
			output_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
		}

		_boxNum = output_attrs[0].dims[1];
		_outputDim2 = output_attrs[0].dims[2];

		if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			//LOGE("model is NCHW input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputChannel = input_attrs[0].dims[1];
			_inputHeight = input_attrs[0].dims[2];
			_inputWidth = input_attrs[0].dims[3];
		}
		else
		{
			//LOGE("model is NHWC input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputHeight = input_attrs[0].dims[1];
			_inputWidth = input_attrs[0].dims[2];
			_inputChannel = input_attrs[0].dims[3];
		}
		
		_rgaInterfacePtr = rgaInterfacePtr;
		
		//this->rgamat_det_pad.handle = _rgaInterfacePtr->malloc_rga(&this->rgamat_det_pad.viraddr, &this->rgamat_det_pad.phyaddr,this->_inputWidth, this->_inputHeight, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);		
		
		input_attrs[0].type = RKNN_TENSOR_UINT8;
		// default fmt is NHWC, npu only support NHWC in zero copy mode
		input_attrs[0].fmt = RKNN_TENSOR_NHWC;
		input_mems[0] = rknn_create_mem(ctx, input_attrs[0].size_with_stride);

		int output_size = output_attrs[0].n_elems * sizeof(float);
		// default output type is depend on model, this require float32 to compute top5
		output_attrs[0].type = RKNN_TENSOR_FLOAT32;
		output_mems[0]  = rknn_create_mem(ctx, output_size);

		#define ALIGN 8
		int wstride = _inputWidth + (ALIGN - _inputWidth % ALIGN) % ALIGN;
  		int hstride = _inputHeight;

		if (_rga_flag)
		{
			_rgaInterfacePtr->wrapbuffer_fd(input_mems[0]->fd, _inputWidth, _inputHeight, wstride, hstride, RK_FORMAT_RGB_888); 			
			ret = rknn_set_io_mem(ctx, input_mems[0], &input_attrs[0]);		
			if (ret < 0) {
				LOGE("rknn_set_io_mem fail! ret=%d\n", ret);
				return -1;
			}     

			int outwstride = _boxNum + (ALIGN - _boxNum % ALIGN) % ALIGN;
  			int outhstride = _outputDim2;
			_rgaInterfacePtr->wrapbuffer_fd(output_mems[0]->fd, _boxNum, _outputDim2, outwstride, outhstride, RK_FORMAT_RGBA_8888); 			
			
			if (_bqat == false)
			{
				ret = rknn_set_io_mem(ctx, output_mems[0], &output_attrs[0]);		
				if (ret < 0) {
					LOGE("rknn_set_io_mem fail! ret=%d\n", ret);
					return -1;
				} 
			}
		}	

		if (_bqat)
		{
			_boxNum = output_attrs[0].dims[2];
			_classNum = output_attrs[0].dims[1];
			_boxDim = output_attrs[1].dims[1];
		}

		return CZCV_OK;
    }


	std::string Yolov10RKNN::fdLoadFile(std::string path) 
	{
		std::ifstream file(path, std::ios::binary);
		if (file.is_open()) {
			file.seekg(0, file.end);
			int size = file.tellg();
			char* content = new char[size];
			file.seekg(0, file.beg);
			file.read(content, size);
			std::string fileContent;
			fileContent.assign(content, size);
			delete[] content;
			file.close();
			return fileContent;
		}
		else {
			return "";
		}
	}
	
	static std::vector<int> topk(std::vector<float> & scores, int k)
	{
		std::vector<int> indices(scores.size());
		std::iota(indices.begin(), indices.end(), 0); // Fill with 0, 1, ..., n-1

		// Sort indices based on the corresponding values in scores
		std::partial_sort(indices.begin(), indices.begin() + k, indices.end(),
			[&scores](int a, int b) { return scores[a] > scores[b]; });

		return std::vector<int>(indices.begin(), indices.begin() + k);
	}

	static float computeIOU(const Rectf& boxA, const Rectf& boxB)
	{
		float xA = (std::max)(boxA.x0, boxB.x0);
		float yA = (std::max)(boxA.y0, boxB.y0);
		float xB = (std::min)(boxA.x1, boxB.x1);
		float yB = (std::min)(boxA.y1, boxB.y1);
		
		float interArea = (std::max)(0.f, xB - xA) * (std::max)(0.f, yB - yA);

		float widthA = boxA.x1 - boxA.x0;
		float heightA = boxA.y1 - boxA.y0;
		float widthB = boxB.x1 - boxB.x0;
		float heightB = boxB.y1 - boxB.y0;
		float boxAArea = widthA * heightA;
		float boxBArea = widthB * heightB;

		float iou = static_cast<float>(interArea) / (boxAArea + boxBArea - interArea + 1.0e-6);
		return iou;
	}

	static float deqnt_affine_to_f32(int8_t qnt, int32_t zp, float scale) { return ((float)qnt - (float)zp) * scale; }

	Status Yolov10RKNN::run_sub_rga_qat(float scale, std::vector<DetectionResult>& results_filtered)
	{
		int offset = 32;
		int ret;
		rknn_output outputs[io_num.n_output];
		memset(outputs, 0, sizeof(outputs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			outputs[i].index = i;
			outputs[i].want_float = 1;
		}
		
		// 执行推理
		ret = rknn_run(ctx, NULL);		
		
		ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);

		float * fdata = (float*)malloc(output_attrs[0].dims[0] * output_attrs[0].dims[1] * output_attrs[0].dims[2] * sizeof(float));

		std::vector<float> out_scales;
		std::vector<int32_t> out_zps;
		for (int i = 0; i < io_num.n_output; ++i)
		{
			out_scales.push_back(output_attrs[i].scale);
			out_zps.push_back(output_attrs[i].zp);
		}

		for (int i = 0; i < output_attrs[0].dims[0] * output_attrs[0].dims[1] * output_attrs[0].dims[2]; i++)
		{
			fdata[i] = deqnt_affine_to_f32(((int8_t *)outputs[0].buf)[i], out_zps[0], out_scales[0]);
		}

		float * fdatabox = (float*)malloc(output_attrs[1].dims[0] * output_attrs[1].dims[1] * output_attrs[1].dims[2] * sizeof(float));

		for (int i = 0; i < output_attrs[1].dims[0] * output_attrs[1].dims[1] * output_attrs[1].dims[2]; i++)
		{
			fdatabox[i] = deqnt_affine_to_f32(((int8_t *)outputs[1].buf)[i], out_zps[1], out_scales[1]);
		}
		
		std::vector<DetectionResult> results;
		std::vector<float> max_scores;
		for (size_t i = 0;i < _boxNum; i++)
		{
			std::vector<float> scores;
			for (size_t n = 0; n < _classNum; n++)
			{
				scores.push_back(fdata[i + n * _boxNum]);
			}
			//max_scores.push_back(*std::max_element(fdata + i * _classNum + 4, fdata + i * _classNum + _classNum));
		
			max_scores.push_back(*std::max_element(scores.data(), scores.data() + _classNum));

			//printf("%d %f %f ", i, max_scores[i], scores[0]);
		}

	
		std::vector<int> indices = topk(max_scores, _maxDet);
		max_scores.clear();

		for (auto i : indices)
		{
			std::vector<float> scores;
			for (size_t n = 0; n < _classNum; n++)
			{
				scores.push_back(fdata[i + n * _boxNum]);
			}
			max_scores.insert(max_scores.end(), scores.begin(), scores.end());
			//max_scores.insert(max_scores.end(), fdata + i * _classNum + 4, fdata + i * _classNum + _classNum);
		}

		std::vector<int> indices2 = topk(max_scores, _maxDet);


		for (auto i : indices2)
		{
			int boxid = indices[i / _classNum];
			int labelid = i % _classNum;
			DetectionResult result;
			result.rect.x0 = fdatabox[boxid];
			result.rect.y0 = fdatabox[boxid + _boxNum];
			result.rect.x1 = fdatabox[boxid + 2 * _boxNum];
			result.rect.y1 = fdatabox[boxid + 3 * _boxNum];
			result.score = fdata[boxid + labelid * _boxNum];
			result.label = labelid;
			//printf("boxid: %d, labelid: %d, score: %f\n", boxid, labelid, result.score);
			results.push_back(result);
		}

		std::vector<DetectionResult> results_nms;
		for (auto& result: results)
		{
			if (result.score > _confThres)
			{
				results_nms.push_back(result);
			}
		}

		std::sort(results_nms.begin(), results_nms.end(), [](DetectionResult & a, DetectionResult & b) { return a.score > b.score; });

		std::vector<bool> isSuppressed(results_nms.size(), false);
		for (size_t i = 0; i < results_nms.size(); ++i)
		{
			for (size_t j = i + 1; j < results_nms.size(); ++j)
			{
				if (isSuppressed[j]) 
				{
					continue;
				} 
					
				float iou = computeIOU(results_nms[i].rect, results_nms[j].rect);
				if (iou > _nmsThres)
				{
					isSuppressed[j] = true;
				}
			}
		}

		for (int i = 0; i < results_nms.size(); ++i)
		{
			if (!isSuppressed[i])
			{
				if (results_nms[i].rect.x0 < -offset || results_nms[i].rect.x1 < -offset)
				{
					continue;
				}
				if (results_nms[i].rect.y0 < -offset || results_nms[i].rect.y1 < -offset)
				{
					continue;
				}
				
				if (results_nms[i].rect.x0 > _inputWidth + offset || results_nms[i].rect.x1 > _inputWidth + offset)
				{
					continue;
				}
				if (results_nms[i].rect.y0 > _inputHeight + offset || results_nms[i].rect.y1 > _inputHeight + offset)
				{
					continue;
				}

				DetectionResult & result = results_nms[i];
				result.rect.x0 /= scale;
				result.rect.y0 /= scale;
				result.rect.x1 /= scale;
				result.rect.y1 /= scale;
				results_filtered.push_back(result);
			}
		}

		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);

        return CZCV_OK;
	}

	Status Yolov10RKNN::run_sub_rga(float scale, std::vector<DetectionResult>& results_filtered)
	{
		if (_bqat)
		{
			return run_sub_rga_qat(scale, results_filtered);
		}

		int offset = 32;
		int ret;
		rknn_output outputs[io_num.n_output];
		memset(outputs, 0, sizeof(outputs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			outputs[i].index = i;
			outputs[i].want_float = 1;
		}
		
		// 执行推理
		ret = rknn_run(ctx, NULL);		
		
		//ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);

		//float* fdata = (float*)outputs[0].buf;

		float* fdata = (float *)output_mems[0]->virt_addr;

		std::vector<float> max_scores;
		for (size_t i = 0;i < _boxNum; i++)
		{
			max_scores.push_back(*std::max_element(fdata + i * _outputDim2 + 4, fdata + i * _outputDim2 + _outputDim2));
		}

		std::vector<int> indices = topk(max_scores, _maxDet);
		max_scores.clear();
		for (auto i : indices)
		{
			max_scores.insert(max_scores.end(), fdata + i * _outputDim2 + 4, fdata + i * _outputDim2 + _outputDim2);
		}

		std::vector<int> indices2 = topk(max_scores, _maxDet);
		std::vector<DetectionResult> results;
		for (auto i : indices2)
		{
			int boxid = indices[i / (_outputDim2 - 4)];
			int labelid = i % (_outputDim2 - 4);
			DetectionResult result;
			result.rect.x0 = fdata[boxid * _outputDim2 + 0];
			result.rect.y0 = fdata[boxid * _outputDim2 + 1];
			result.rect.x1 = fdata[boxid * _outputDim2 + 2];
			result.rect.y1 = fdata[boxid * _outputDim2 + 3];
			result.score = fdata[boxid * _outputDim2 + 4 + labelid];
			result.label = labelid;
			results.push_back(result);
		}

		std::vector<DetectionResult> results_nms;
		for (auto& result: results)
		{
			if (result.score > _confThres)
			{
				results_nms.push_back(result);
			}
		}

		std::sort(results_nms.begin(), results_nms.end(), [](DetectionResult & a, DetectionResult & b) { return a.score > b.score; });

		std::vector<bool> isSuppressed(results_nms.size(), false);
		for (size_t i = 0; i < results_nms.size(); ++i)
		{
			for (size_t j = i + 1; j < results_nms.size(); ++j)
			{
				if (isSuppressed[j]) 
				{
					continue;
				} 
					
				float iou = computeIOU(results_nms[i].rect, results_nms[j].rect);
				if (iou > _nmsThres)
				{
					isSuppressed[j] = true;
				}
			}
		}

		for (int i = 0; i < results_nms.size(); ++i)
		{
			if (!isSuppressed[i])
			{
				if (results_nms[i].rect.x0 < -offset || results_nms[i].rect.x1 < -offset)
				{
					continue;
				}
				if (results_nms[i].rect.y0 < -offset || results_nms[i].rect.y1 < -offset)
				{
					continue;
				}
				
				if (results_nms[i].rect.x0 > _inputWidth + offset || results_nms[i].rect.x1 > _inputWidth + offset)
				{
					continue;
				}
				if (results_nms[i].rect.y0 > _inputHeight + offset || results_nms[i].rect.y1 > _inputHeight + offset)
				{
					continue;
				}

				DetectionResult & result = results_nms[i];
				result.rect.x0 /= scale;
				result.rect.y0 /= scale;
				result.rect.x1 /= scale;
				result.rect.y1 /= scale;
				results_filtered.push_back(result);
			}
		}

		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);

        return CZCV_OK;
	}

	
	Status Yolov10RKNN::run_sub(const cv::Mat & bgr, std::vector<DetectionResult>& results_filtered)
	{
		int offset = 32;
		int frame_h = bgr.rows;
		int frame_w = bgr.cols;
		int frame_channels = bgr.channels();

		int img_height = frame_h;
		if (frame_channels == 1)
		{
			img_height = img_height * 2 / 3;
		}
		int img_width = frame_w;
		int w = img_width;
		int h = img_height;
		float scale = 1.f;
		if (w > h)
		{
			scale = (float)_inputWidth / w;
			w = _inputWidth;
			h = h * scale;			
		}
		else
		{
			scale = (float)_inputHeight / h;
			h = _inputHeight;
			w = w * scale;
		}
	
		cv::Mat mat_rs;
		int ret;
	
		//int ret = _rgaInterfacePtr->mat_fill(rgamat_det_pad.phyaddr,_inputWidth,_inputHeight,114,114,114);
		//ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.get_input_phyadrr(),rgamat_det_pad.phyaddr,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows,_inputWidth,_inputHeight,0,0,w,h);

		//LOGE("_rgaInterfacePtr->mat_fill\n");
		// ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);
		// //LOGE("_rgaInterfacePtr->cropScaleRgb\n");
		// ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.get_input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows,_inputWidth,_inputHeight,0,0,w,h);
	
		cv::resize(bgr, mat_rs, cv::Size(-1, -1), scale, scale);
		int padw = _inputWidth - mat_rs.cols;
		int padh = _inputHeight - mat_rs.rows;
		cv::copyMakeBorder(mat_rs, mat_rs, 0, padh, 0, padw, cv::BORDER_CONSTANT, cv::Scalar(0, 0, 0));
	
		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = _inputWidth * _inputHeight * _inputChannel;
		inputs[0].fmt = RKNN_TENSOR_NHWC;
		inputs[0].pass_through = 0;
		//inputs[0].buf = rgamat_det_pad.viraddr;
		inputs[0].buf = mat_rs.data;
		//memcpy(input_mems[0]->virt_addr, rgamat_det_pad.viraddr, input_attrs[0].size);

		rknn_inputs_set(ctx, io_num.n_input, inputs);

		rknn_output outputs[io_num.n_output];
		memset(outputs, 0, sizeof(outputs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			outputs[i].index = i;
			outputs[i].want_float = 1;
		}
		
		// 执行推理
		ret = rknn_run(ctx, NULL);		
		
		ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);

		float* fdata = (float*)outputs[0].buf;

		std::vector<float> max_scores;
		for (size_t i = 0;i < _boxNum; i++)
		{
			max_scores.push_back(*std::max_element(fdata + i * _outputDim2 + 4, fdata + i * _outputDim2 + _outputDim2));
		}

		std::vector<int> indices = topk(max_scores, _maxDet);
		max_scores.clear();
		for (auto i : indices)
		{
			max_scores.insert(max_scores.end(), fdata + i * _outputDim2 + 4, fdata + i * _outputDim2 + _outputDim2);
		}

		std::vector<int> indices2 = topk(max_scores, _maxDet);
		std::vector<DetectionResult> results;
		for (auto i : indices2)
		{
			int boxid = indices[i / (_outputDim2 - 4)];
			int labelid = i % (_outputDim2 - 4);
			DetectionResult result;
			result.rect.x0 = fdata[boxid * _outputDim2 + 0];
			result.rect.y0 = fdata[boxid * _outputDim2 + 1];
			result.rect.x1 = fdata[boxid * _outputDim2 + 2];
			result.rect.y1 = fdata[boxid * _outputDim2 + 3];
			result.score = fdata[boxid * _outputDim2 + 4 + labelid];
			result.label = labelid;
			results.push_back(result);
		}

		std::vector<DetectionResult> results_nms;
		for (auto& result: results)
		{
			if (result.score > _confThres)
			{
				results_nms.push_back(result);
			}
		}

		std::sort(results_nms.begin(), results_nms.end(), [](DetectionResult & a, DetectionResult & b) { return a.score > b.score; });

		std::vector<bool> isSuppressed(results_nms.size(), false);
		for (size_t i = 0; i < results_nms.size(); ++i)
		{
			for (size_t j = i + 1; j < results_nms.size(); ++j)
			{
				if (isSuppressed[j]) 
				{
					continue;
				} 
					
				float iou = computeIOU(results_nms[i].rect, results_nms[j].rect);
				if (iou > _nmsThres)
				{
					isSuppressed[j] = true;
				}
			}
		}

		for (int i = 0; i < results_nms.size(); ++i)
		{
			if (!isSuppressed[i])
			{
				if (results_nms[i].rect.x0 < -offset || results_nms[i].rect.x1 < -offset)
				{
					continue;
				}
				if (results_nms[i].rect.y0 < -offset || results_nms[i].rect.y1 < -offset)
				{
					continue;
				}
				
				if (results_nms[i].rect.x0 > _inputWidth + offset || results_nms[i].rect.x1 > _inputWidth + offset)
				{
					continue;
				}
				if (results_nms[i].rect.y0 > _inputHeight + offset || results_nms[i].rect.y1 > _inputHeight + offset)
				{
					continue;
				}

				DetectionResult & result = results_nms[i];
				result.rect.x0 /= scale;
				result.rect.y0 /= scale;
				result.rect.x1 /= scale;
				result.rect.y1 /= scale;
				results_filtered.push_back(result);

				// float x0 = result.rect.x0 / scale;
				// float y0 = result.rect.y0 / scale;
				// float x1 = result.rect.x1 / scale;
				// float y1 = result.rect.y1 / scale;

				// // clip
				// x0 = (std::max)((std::min)(x0, (float)(img_width - 1)), 0.f);
				// y0 = (std::max)((std::min)(y0, (float)(img_height - 1)), 0.f);
				// x1 = (std::max)((std::min)(x1, (float)(img_width - 1)), 0.f);
				// y1 = (std::max)((std::min)(y1, (float)(img_height - 1)), 0.f);

				// BboxF bboxF(x0, y0, x1, y1, result.score, result.label);
				// inputOutput.push_one_bbox(bboxF); 
			}
		}

		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);

        return CZCV_OK;
	}

	bool save_flag = false;
	int saveid = 0;
    Status Yolov10RKNN::run(TrackerInputOutput &inputOutput)
    {
		czcv_camera::RgaMat rgamat_det_pad;
		rgamat_det_pad.handle = _rgaInterfacePtr->malloc_rga(&rgamat_det_pad.viraddr, &rgamat_det_pad.phyaddr,_inputWidth, _inputHeight, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
		inputOutput.gestureResults().clear();
		cv::Mat bgr;
        inputOutput.ref_frame_to(bgr);

		int frame_h = bgr.rows;
		int frame_w = bgr.cols;
		int frame_channels = bgr.channels();

		int img_height = frame_h;
		if (frame_channels == 1)
		{
			img_height = img_height * 2 / 3;
		}
		int img_width = frame_w;
		
		inputOutput.gestureResults().clear();
		
		int person_num = 0;
		for (auto box: inputOutput.in_bbox())
		{
			if (box.class_id() != 0)
			{
				continue;
			}
			person_num++;
		}

		std::vector<DetectionResult> results_filtered;

		if (person_num > 0)
		{
			int row_num = ceil(sqrt(person_num / 2.5f));
			int col_num = (person_num + row_num - 1) / row_num;
			int dstw = _inputWidth / col_num;
			int dsth = _inputHeight / row_num;
			float ratio = (float)dstw / dsth;
			int box_id = 0;
			std::vector<float> scales;
			int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);		
			ret = _rgaInterfacePtr->mat_fill(rgamat_det_pad.phyaddr,_inputWidth,_inputHeight,0,0,0);

			//LOGE("Yolov10RKNN::run: person_num %d %d %d %d %d",person_num, row_num, col_num, dstw, dsth);	
			for (size_t i = 0; i < inputOutput.in_bbox().size(); i++)
			{
				auto box = inputOutput.in_bbox()[i];
				if (box.class_id() != 0)
				{
					continue;
				}

				box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
				
				int col_id = box_id % col_num;
				int row_id = box_id / col_num;
				int dstx = col_id * _inputWidth / col_num;
				int dsty = row_id * _inputHeight / row_num;

				int w = box.width();
				int h = box.height();
				float scale = 1.f;
				if (w > h * ratio)
				{
					scale = (float)dstw / w;
					w = dstw;
					h = h * scale;			
				}
				else
				{
					scale = (float)dsth / h;
					h = dsth;
					w = w * scale;
				}
				scales.push_back(scale);
				box_id++;	
				
				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,dstx,dsty,w,h);
				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),rgamat_det_pad.phyaddr,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,dstx,dsty,w,h);
				//LOGE("Yolov10RKNN::run: box_id %d %d %d %d %d",box_id, dstx, dsty, w, h);
			}

			// saveid++;
			// if (false == save_flag && saveid > 15 && scales.size() > 1)
			// {
			// 	cv::Mat debug = cv::Mat(_inputHeight, _inputWidth, CV_8UC3, rgamat_det_pad.viraddr);
			// 	save_flag = true;
			// 	cv::imwrite("/mnt/img/test.jpg", debug);
			// }

			run_sub_rga(1.0f, results_filtered);

			LOGE("Yolov10RKNN::run: results_filtered size: %d, person_num: %d\n", results_filtered.size(), person_num);

			// 重建box信息用于坐标还原
			std::vector<BboxF> person_boxes;
			std::vector<float> box_scales;
			std::vector<int> box_dst_positions; // 存储每个box在拼接图像中的位置信息

			box_id = 0;
			for (size_t i = 0; i < inputOutput.in_bbox().size(); i++)
			{
				auto box = inputOutput.in_bbox()[i];
				if (box.class_id() != 0)
				{
					continue;
				}

				box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
				person_boxes.push_back(box);

				// 重新计算scale和位置信息
				int col_id = box_id % col_num;
				int row_id = box_id / col_num;
				int dstx = col_id * _inputWidth / col_num;
				int dsty = row_id * _inputHeight / row_num;

				int w = box.width();
				int h = box.height();
				float scale = 1.f;
				if (w > h * ratio)
				{
					scale = (float)dstw / w;
					w = dstw;
					h = h * scale;
				}
				else
				{
					scale = (float)dsth / h;
					h = dsth;
					w = w * scale;
				}

				box_scales.push_back(scale);
				box_dst_positions.push_back(dstx);
				box_dst_positions.push_back(dsty);
				box_dst_positions.push_back(w);
				box_dst_positions.push_back(h);

				box_id++;
			}

			for (auto result: results_filtered)
			{
				LOGD("Yolov10RKNN::run result: %f, %f, %f, %f, %d\n", result.rect.x0, result.rect.y0, result.rect.x1, result.rect.y1, result.label);

				// 判断result属于哪个box
				float result_center_x = (result.rect.x0 + result.rect.x1) / 2.0f;
				float result_center_y = (result.rect.y0 + result.rect.y1) / 2.0f;

				int target_box_id = -1;
				for (int i = 0; i < person_boxes.size(); i++)
				{
					int dstx = box_dst_positions[i * 4];
					int dsty = box_dst_positions[i * 4 + 1];
					int w = box_dst_positions[i * 4 + 2];
					int h = box_dst_positions[i * 4 + 3];

					// 检查result的中心点是否在当前box的区域内
					if (result_center_x >= dstx && result_center_x < dstx + w &&
						result_center_y >= dsty && result_center_y < dsty + h)
					{
						target_box_id = i;
						break;
					}
				}

				if (target_box_id >= 0)
				{
					// 还原坐标到原始box坐标系
					BboxF& original_box = person_boxes[target_box_id];
					float scale = box_scales[target_box_id];
					int dstx = box_dst_positions[target_box_id * 4];
					int dsty = box_dst_positions[target_box_id * 4 + 1];

					LOGD("Yolov10RKNN::run coordinate transform: target_box_id=%d, scale=%f, dstx=%d, dsty=%d\n",
						target_box_id, scale, dstx, dsty);
					LOGD("Yolov10RKNN::run original_box: xmin=%f, ymin=%f, xmax=%f, ymax=%f\n",
						original_box.xmin(), original_box.ymin(), original_box.xmax(), original_box.ymax());

					// 将坐标从拼接图像坐标系转换到box内坐标系
					float local_x0 = result.rect.x0 - dstx;
					float local_y0 = result.rect.y0 - dsty;
					float local_x1 = result.rect.x1 - dstx;
					float local_y1 = result.rect.y1 - dsty;

					// 缩放回原始尺寸
					local_x0 /= scale;
					local_y0 /= scale;
					local_x1 /= scale;
					local_y1 /= scale;

					// 转换到原始图像坐标系
					float x0 = (std::max)((std::min)(local_x0 + original_box.xmin(), (float)(img_width - 1)), 0.f);
					float y0 = (std::max)((std::min)(local_y0 + original_box.ymin(), (float)(img_height - 1)), 0.f);
					float x1 = (std::max)((std::min)(local_x1 + original_box.xmin(), (float)(img_width - 1)), 0.f);
					float y1 = (std::max)((std::min)(local_y1 + original_box.ymin(), (float)(img_height - 1)), 0.f);

					LOGD("Yolov10RKNN::run final coordinates: x0=%f, y0=%f, x1=%f, y1=%f\n", x0, y0, x1, y1);

					// 创建手势识别结果
					stGestureRecResult recresult;
					recresult.rectf.x0 = x0;
					recresult.rectf.y0 = y0;
					recresult.rectf.x1 = x1;
					recresult.rectf.y1 = y1;
					recresult.palm_score = result.score;
					recresult.clsid = result.label;
					recresult.det_instance_id = original_box.instance_id();
					inputOutput.gestureResults().push_back(recresult);
				}
				else
				{
					LOGD("Yolov10RKNN::run: result not matched to any box, center=(%f, %f)\n",
						result_center_x, result_center_y);
				}
			}
		}
		
		if (rgamat_det_pad.handle != NULL)
		{
			int ret = _rgaInterfacePtr->free(rgamat_det_pad.handle);
			rgamat_det_pad.handle = nullptr;
		} 
		
		// for (auto box: inputOutput.in_bbox())
		// {
		// 	if (box.class_id() != 0)
		// 	{
		// 		continue;
		// 	}

		// 	box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);

		// 	std::vector<DetectionResult> results_filtered;

		// 	if (_rga_flag)
		// 	{
		// 		int w = box.width();
		// 		int h = box.height();
		// 		float scale = 1.f;
		// 		if (w > h)
		// 		{
		// 			scale = (float)_inputWidth / w;
		// 			w = _inputWidth;
		// 			h = h * scale;			
		// 		}
		// 		else
		// 		{
		// 			scale = (float)_inputHeight / h;
		// 			h = _inputHeight;
		// 			w = w * scale;
		// 		}

		// 		int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);			
		// 		ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,0,0,w,h);

		// 		run_sub_rga(scale, results_filtered);
		// 	}		
		// 	else
		// 	{
		// 		cv::Mat roiimg = bgr.colRange(box.xmin(), box.xmax() + 1).rowRange(box.ymin(), box.ymax() + 1).clone();		
		// 		run_sub(roiimg, results_filtered);
		// 	}		
			
		// 	for (auto & result: results_filtered)
		// 	{
		// 		float x0 = (std::max)((std::min)(result.rect.x0 + box.xmin(), (float)(img_width - 1)), 0.f);
		// 		float y0 = (std::max)((std::min)(result.rect.y0 + box.ymin(), (float)(img_height - 1)), 0.f);
		// 		float x1 = (std::max)((std::min)(result.rect.x1 + box.xmin(), (float)(img_width - 1)), 0.f);
		// 		float y1 = (std::max)((std::min)(result.rect.y1 + box.ymin(), (float)(img_height - 1)), 0.f);

		// 		stGestureRecResult recresult;
		// 		recresult.rectf.x0 = x0;
		// 		recresult.rectf.y0 = y0;
		// 		recresult.rectf.x1 = x1;
		// 		recresult.rectf.y1 = y1;
		// 		recresult.palm_score = result.score;
		// 		recresult.clsid = result.label;
		// 		recresult.det_instance_id = box.instance_id();
		// 		inputOutput.gestureResults().push_back(recresult);
		// 	}
		// }

		// LOGE("Yolov10RKNN::run: %d\n", inputOutput.gestureResults().size());
		// for (auto result: inputOutput.gestureResults())
		// {
		// 	LOGD("Yolov10RKNN::run result: %f, %f, %f, %f, %d\n", result.rectf.x0, result.rectf.y0, result.rectf.x1, result.rectf.y1, result.clsid);
		// }

		return CZCV_OK;		
    }

}//namespace czcv_mobile

