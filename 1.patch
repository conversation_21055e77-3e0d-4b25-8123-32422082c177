diff --git a/lib/src/hand/rknn_yolov10.cpp b/lib/src/hand/rknn_yolov10.cpp
index bf9ff79..ac502e9 100644
--- a/lib/src/hand/rknn_yolov10.cpp
+++ b/lib/src/hand/rknn_yolov10.cpp
@@ -642,8 +642,12 @@ namespace czcv_camera
         return CZCV_OK;
 	}
 
+	bool save_flag = false;
+	int saveid = 0;
     Status Yolov10RKNN::run(TrackerInputOutput &inputOutput)
     {
+		czcv_camera::RgaMat rgamat_det_pad;
+		rgamat_det_pad.handle = _rgaInterfacePtr->malloc_rga(&rgamat_det_pad.viraddr, &rgamat_det_pad.phyaddr,_inputWidth, _inputHeight, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
 		inputOutput.gestureResults().clear();
 		cv::Mat bgr;
         inputOutput.ref_frame_to(bgr);
@@ -660,65 +664,277 @@ namespace czcv_camera
 		int img_width = frame_w;
 		
 		inputOutput.gestureResults().clear();
-	
+		
+		int person_num = 0;
 		for (auto box: inputOutput.in_bbox())
 		{
 			if (box.class_id() != 0)
 			{
 				continue;
 			}
+			person_num++;
+		}
 
-			box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+		std::vector<DetectionResult> results_filtered;
 
-			std::vector<DetectionResult> results_filtered;
+		if (person_num > 0)
+		{
+			int row_num = ceil(sqrt(person_num / 2.5f));
+			int col_num = (person_num + row_num - 1) / row_num;
+			int dstw = _inputWidth / col_num;
+			int dsth = _inputHeight / row_num;
+			float ratio = (float)dstw / dsth;
+			int box_id = 0;
+			std::vector<float> scales;
+			int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);		
+			ret = _rgaInterfacePtr->mat_fill(rgamat_det_pad.phyaddr,_inputWidth,_inputHeight,0,0,0);
 
-			if (_rga_flag)
+			//LOGE("Yolov10RKNN::run: person_num %d %d %d %d %d",person_num, row_num, col_num, dstw, dsth);	
+			for (size_t i = 0; i < inputOutput.in_bbox().size(); i++)
 			{
+				auto box = inputOutput.in_bbox()[i];
+				if (box.class_id() != 0)
+				{
+					continue;
+				}
+
+				box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+				
+				int col_id = box_id % col_num;
+				int row_id = box_id / col_num;
+				int dstx = col_id * _inputWidth / col_num;
+				int dsty = row_id * _inputHeight / row_num;
+
 				int w = box.width();
 				int h = box.height();
 				float scale = 1.f;
-				if (w > h)
+				if (w > h * ratio)
 				{
-					scale = (float)_inputWidth / w;
-					w = _inputWidth;
+					scale = (float)dstw / w;
+					w = dstw;
 					h = h * scale;			
 				}
 				else
 				{
-					scale = (float)_inputHeight / h;
-					h = _inputHeight;
+					scale = (float)dsth / h;
+					h = dsth;
 					w = w * scale;
 				}
+				scales.push_back(scale);
+				box_id++;	
+				
+				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,dstx,dsty,w,h);
+				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),rgamat_det_pad.phyaddr,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,dstx,dsty,w,h);
+				//LOGE("Yolov10RKNN::run: box_id %d %d %d %d %d",box_id, dstx, dsty, w, h);
+			}
+
+			// saveid++;
+			// if (false == save_flag && saveid > 15 && scales.size() > 1)
+			// {
+			// 	cv::Mat debug = cv::Mat(_inputHeight, _inputWidth, CV_8UC3, rgamat_det_pad.viraddr);
+			// 	save_flag = true;
+			// 	cv::imwrite("/mnt/img/test.jpg", debug);
+			// }
+
+			run_sub_rga(1.0f, results_filtered);
 
-				int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);			
-				ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,0,0,w,h);
+			LOGE("Yolov10RKNN::run: results_filtered size: %d, person_num: %d\n", results_filtered.size(), person_num);
 
-				run_sub_rga(scale, results_filtered);
-			}		
-			else
+			// 閲嶅缓box淇℃伅鐢ㄤ簬鍧愭爣杩樺師
+			std::vector<BboxF> person_boxes;
+			std::vector<float> box_scales;
+			std::vector<int> box_dst_positions; // 瀛樺偍姣忎釜box鍦ㄦ嫾鎺ュ浘鍍忎腑鐨勪綅缃俊鎭?+
+			box_id = 0;
+			for (size_t i = 0; i < inputOutput.in_bbox().size(); i++)
 			{
-				cv::Mat roiimg = bgr.colRange(box.xmin(), box.xmax() + 1).rowRange(box.ymin(), box.ymax() + 1).clone();		
-				run_sub(roiimg, results_filtered);
-			}		
-			
-			for (auto & result: results_filtered)
+				auto box = inputOutput.in_bbox()[i];
+				if (box.class_id() != 0)
+				{
+					continue;
+				}
+
+				box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+				person_boxes.push_back(box);
+
+				// 閲嶆柊璁＄畻scale鍜屼綅缃俊鎭?+				int col_id = box_id % col_num;
+				int row_id = box_id / col_num;
+				int dstx = col_id * _inputWidth / col_num;
+				int dsty = row_id * _inputHeight / row_num;
+
+				int w = box.width();
+				int h = box.height();
+				float scale = 1.f;
+				if (w > h * ratio)
+				{
+					scale = (float)dstw / w;
+					w = dstw;
+					h = h * scale;
+				}
+				else
+				{
+					scale = (float)dsth / h;
+					h = dsth;
+					w = w * scale;
+				}
+
+				box_scales.push_back(scale);
+				box_dst_positions.push_back(dstx);
+				box_dst_positions.push_back(dsty);
+				box_dst_positions.push_back(w);
+				box_dst_positions.push_back(h);
+
+				box_id++;
+			}
+
+			for (auto result: results_filtered)
 			{
-				float x0 = (std::max)((std::min)(result.rect.x0 + box.xmin(), (float)(img_width - 1)), 0.f);
-				float y0 = (std::max)((std::min)(result.rect.y0 + box.ymin(), (float)(img_height - 1)), 0.f);
-				float x1 = (std::max)((std::min)(result.rect.x1 + box.xmin(), (float)(img_width - 1)), 0.f);
-				float y1 = (std::max)((std::min)(result.rect.y1 + box.ymin(), (float)(img_height - 1)), 0.f);
-
-				stGestureRecResult recresult;
-				recresult.rectf.x0 = x0;
-				recresult.rectf.y0 = y0;
-				recresult.rectf.x1 = x1;
-				recresult.rectf.y1 = y1;
-				recresult.palm_score = result.score;
-				recresult.clsid = result.label;
-				recresult.det_instance_id = box.instance_id();
-				inputOutput.gestureResults().push_back(recresult);
+				LOGD("Yolov10RKNN::run result: %f, %f, %f, %f, %d\n", result.rect.x0, result.rect.y0, result.rect.x1, result.rect.y1, result.label);
+
+				// 鍒ゆ柇result灞炰簬鍝釜box
+				float result_center_x = (result.rect.x0 + result.rect.x1) / 2.0f;
+				float result_center_y = (result.rect.y0 + result.rect.y1) / 2.0f;
+
+				int target_box_id = -1;
+				for (int i = 0; i < person_boxes.size(); i++)
+				{
+					int dstx = box_dst_positions[i * 4];
+					int dsty = box_dst_positions[i * 4 + 1];
+					int w = box_dst_positions[i * 4 + 2];
+					int h = box_dst_positions[i * 4 + 3];
+
+					// 妫€鏌esult鐨勪腑蹇冪偣鏄惁鍦ㄥ綋鍓峛ox鐨勫尯鍩熷唴
+					if (result_center_x >= dstx && result_center_x < dstx + w &&
+						result_center_y >= dsty && result_center_y < dsty + h)
+					{
+						target_box_id = i;
+						break;
+					}
+				}
+
+				if (target_box_id >= 0)
+				{
+					// 杩樺師鍧愭爣鍒板師濮媌ox鍧愭爣绯?+					BboxF& original_box = person_boxes[target_box_id];
+					float scale = box_scales[target_box_id];
+					int dstx = box_dst_positions[target_box_id * 4];
+					int dsty = box_dst_positions[target_box_id * 4 + 1];
+
+					LOGD("Yolov10RKNN::run coordinate transform: target_box_id=%d, scale=%f, dstx=%d, dsty=%d\n",
+						target_box_id, scale, dstx, dsty);
+					LOGD("Yolov10RKNN::run original_box: xmin=%f, ymin=%f, xmax=%f, ymax=%f\n",
+						original_box.xmin(), original_box.ymin(), original_box.xmax(), original_box.ymax());
+
+					// 灏嗗潗鏍囦粠鎷兼帴鍥惧儚鍧愭爣绯昏浆鎹㈠埌box鍐呭潗鏍囩郴
+					float local_x0 = result.rect.x0 - dstx;
+					float local_y0 = result.rect.y0 - dsty;
+					float local_x1 = result.rect.x1 - dstx;
+					float local_y1 = result.rect.y1 - dsty;
+
+					// 缂╂斁鍥炲師濮嬪昂瀵?+					local_x0 /= scale;
+					local_y0 /= scale;
+					local_x1 /= scale;
+					local_y1 /= scale;
+
+					// 杞崲鍒板師濮嬪浘鍍忓潗鏍囩郴
+					float x0 = (std::max)((std::min)(local_x0 + original_box.xmin(), (float)(img_width - 1)), 0.f);
+					float y0 = (std::max)((std::min)(local_y0 + original_box.ymin(), (float)(img_height - 1)), 0.f);
+					float x1 = (std::max)((std::min)(local_x1 + original_box.xmin(), (float)(img_width - 1)), 0.f);
+					float y1 = (std::max)((std::min)(local_y1 + original_box.ymin(), (float)(img_height - 1)), 0.f);
+
+					LOGD("Yolov10RKNN::run final coordinates: x0=%f, y0=%f, x1=%f, y1=%f\n", x0, y0, x1, y1);
+
+					// 鍒涘缓鎵嬪娍璇嗗埆缁撴灉
+					stGestureRecResult recresult;
+					recresult.rectf.x0 = x0;
+					recresult.rectf.y0 = y0;
+					recresult.rectf.x1 = x1;
+					recresult.rectf.y1 = y1;
+					recresult.palm_score = result.score;
+					recresult.clsid = result.label;
+					recresult.det_instance_id = original_box.instance_id();
+					inputOutput.gestureResults().push_back(recresult);
+				}
+				else
+				{
+					LOGD("Yolov10RKNN::run: result not matched to any box, center=(%f, %f)\n",
+						result_center_x, result_center_y);
+				}
 			}
 		}
+		
+		if (rgamat_det_pad.handle != NULL)
+		{
+			int ret = _rgaInterfacePtr->free(rgamat_det_pad.handle);
+			rgamat_det_pad.handle = nullptr;
+		} 
+		
+		// for (auto box: inputOutput.in_bbox())
+		// {
+		// 	if (box.class_id() != 0)
+		// 	{
+		// 		continue;
+		// 	}
+
+		// 	box = box.scale_by(1.2f).clip_by(0, bgr.cols - 1, 0, bgr.rows - 1);
+
+		// 	std::vector<DetectionResult> results_filtered;
+
+		// 	if (_rga_flag)
+		// 	{
+		// 		int w = box.width();
+		// 		int h = box.height();
+		// 		float scale = 1.f;
+		// 		if (w > h)
+		// 		{
+		// 			scale = (float)_inputWidth / w;
+		// 			w = _inputWidth;
+		// 			h = h * scale;			
+		// 		}
+		// 		else
+		// 		{
+		// 			scale = (float)_inputHeight / h;
+		// 			h = _inputHeight;
+		// 			w = w * scale;
+		// 		}
+
+		// 		int ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);			
+		// 		ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,box.xmin(),box.ymin(),box.width(),box.height(),_inputWidth,_inputHeight,0,0,w,h);
+
+		// 		run_sub_rga(scale, results_filtered);
+		// 	}		
+		// 	else
+		// 	{
+		// 		cv::Mat roiimg = bgr.colRange(box.xmin(), box.xmax() + 1).rowRange(box.ymin(), box.ymax() + 1).clone();		
+		// 		run_sub(roiimg, results_filtered);
+		// 	}		
+			
+		// 	for (auto & result: results_filtered)
+		// 	{
+		// 		float x0 = (std::max)((std::min)(result.rect.x0 + box.xmin(), (float)(img_width - 1)), 0.f);
+		// 		float y0 = (std::max)((std::min)(result.rect.y0 + box.ymin(), (float)(img_height - 1)), 0.f);
+		// 		float x1 = (std::max)((std::min)(result.rect.x1 + box.xmin(), (float)(img_width - 1)), 0.f);
+		// 		float y1 = (std::max)((std::min)(result.rect.y1 + box.ymin(), (float)(img_height - 1)), 0.f);
+
+		// 		stGestureRecResult recresult;
+		// 		recresult.rectf.x0 = x0;
+		// 		recresult.rectf.y0 = y0;
+		// 		recresult.rectf.x1 = x1;
+		// 		recresult.rectf.y1 = y1;
+		// 		recresult.palm_score = result.score;
+		// 		recresult.clsid = result.label;
+		// 		recresult.det_instance_id = box.instance_id();
+		// 		inputOutput.gestureResults().push_back(recresult);
+		// 	}
+		// }
+
+		// LOGE("Yolov10RKNN::run: %d\n", inputOutput.gestureResults().size());
+		// for (auto result: inputOutput.gestureResults())
+		// {
+		// 	LOGD("Yolov10RKNN::run result: %f, %f, %f, %f, %d\n", result.rectf.x0, result.rectf.y0, result.rectf.x1, result.rectf.y1, result.clsid);
+		// }
 
 		return CZCV_OK;		
     }
